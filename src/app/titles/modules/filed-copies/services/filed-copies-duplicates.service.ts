import { Injectable } from '@angular/core';
import { LinkedDocumentItem } from '../../../types/linked-document-item.type';
import { duplicateColors } from '../constants/duplicate-colors.constant';
import { GroupItem } from '../types/group-item.type';

@Injectable()
export class FiledCopiesDuplicatesService {
    public assignGroupAttributes(items: LinkedDocumentItem[]): GroupItem<LinkedDocumentItem>[] {
        const groups = this.groupBy(items, 'documentType');
        const result: GroupItem<LinkedDocumentItem>[] = this.assignColorsToDuplicates(groups);

        groups.forEach((groupItems) => {
            if (groupItems.length === 1) {
                result.push(this.assignDefaultAttributes(groupItems[0]));
            }
        });

        return result;
    }

    public assignDefaultAttributes(item: LinkedDocumentItem): GroupItem<LinkedDocumentItem> {
        return {
            ...item,
            bgColor: null,
            borderColor: null,
            isFirst: false,
            isLast: false,
        };
    }

    private groupBy(items: LinkedDocumentItem[], key: keyof LinkedDocumentItem): Map<string, LinkedDocumentItem[]> {
        const groups = new Map<string, LinkedDocumentItem[]>();

        items.forEach((item) => {
            const reference: string = item[key].toString();

            if (!groups.has(reference)) {
                groups.set(reference, []);
            }

            groups.get(reference).push(item);
        });

        return groups;
    }

    private assignColorsToDuplicates(groups: Map<string, LinkedDocumentItem[]>): GroupItem<LinkedDocumentItem>[] {
        const result: GroupItem<LinkedDocumentItem>[] = [];
        let groupIndex = 0;

        groups.forEach((groupItems) => {
            if (groupItems.length > 1) {
                const groupColors = duplicateColors[groupIndex % duplicateColors.length];
                groupIndex++;

                groupItems.forEach((item, index) => {
                    result.push({
                        ...item,
                        bgColor: groupColors.background,
                        borderColor: groupColors.border,
                        isFirst: index === 0,
                        isLast: index === groupItems.length - 1,
                    });
                });
            }
        });

        return result;
    }
}
