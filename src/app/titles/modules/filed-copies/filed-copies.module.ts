import { NgModule } from '@angular/core';
import { FiledCopiesFilterComponent } from './components/filed-copies-filter/filed-copies-filter.component';
import { SharedModule } from '@shared/shared.module';
import { FilterInputComponent } from './components/filter-input/filter-input.component';
import { FormsModule } from '@angular/forms';
import { FiledCopiesDuplicatesService } from './services/filed-copies-duplicates.service';


@NgModule({
    declarations: [
        FiledCopiesFilterComponent,
        FilterInputComponent,
    ],
    imports: [
        SharedModule,
        FormsModule,
    ],
    exports: [
        FiledCopiesFilterComponent,
        FilterInputComponent,
    ],
    providers: [
        FiledCopiesDuplicatesService,
    ],
})
export class FiledCopiesModule {
}
