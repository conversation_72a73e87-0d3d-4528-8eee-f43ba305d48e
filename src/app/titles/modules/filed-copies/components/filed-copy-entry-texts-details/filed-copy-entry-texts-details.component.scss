@use 'assets/sass/variables' as *;

.entry-texts-container {
    padding: 0.86em;
    background-color: lighten($gray, 18%);
    border-radius: $primary-border-radius;
}

.entry-texts-grid {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 0.86em;
    align-items: start;
}

.entry-key {
    min-width: 30px;
}

.entry-value {
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.entry-separator {
    grid-column: 1 / -1;
    height: 1px;
    background-color: lighten($gray, 12%);
}
