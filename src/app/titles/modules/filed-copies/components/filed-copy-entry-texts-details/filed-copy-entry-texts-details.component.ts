import { Component, Input } from '@angular/core';

@Component({
    selector: 'avl-filed-copy-entry-texts-details',
    templateUrl: './filed-copy-entry-texts-details.component.html',
    styleUrls: ['./filed-copy-entry-texts-details.component.scss'],
})
export class FiledCopyEntryTextsDetailsComponent {
    @Input()
    public entryTexts: { [key: string]: string };


    public getKeys(): string[] {
        return Object.keys(this.entryTexts);
    }
//"entryTexts": {
//                 "A2": "(05.06.2013) The title includes any legal easements referred to in clause LR11.1 of the registered lease but is subject to any rights that are granted or reserved by the lease and affect the registered land."
//             },
}
